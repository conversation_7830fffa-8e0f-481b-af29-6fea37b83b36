"""
Exemplos práticos de uso das bibliotecas de análise de dados
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
from scipy import optimize
import warnings
warnings.filterwarnings('ignore')


def numpy_examples():
    """Exemplos com NumPy"""
    print("🔢 Exemplos NumPy")
    print("=" * 30)
    
    # Arrays e operações básicas
    arr = np.array([1, 2, 3, 4, 5])
    print(f"Array: {arr}")
    print(f"Quadrado: {arr**2}")
    print(f"Soma: {np.sum(arr)}")
    print(f"Média: {np.mean(arr)}")
    print(f"Desvio padrão: {np.std(arr)}")
    
    # Matriz 2D
    matrix = np.random.randn(3, 3)
    print(f"\nMatriz 3x3:\n{matrix}")
    print(f"Determinante: {np.linalg.det(matrix):.4f}")
    
    # Operações matemáticas
    x = np.linspace(0, 2*np.pi, 100)
    y = np.sin(x)
    print(f"\nSeno de 0 a 2π - Min: {y.min():.3f}, Max: {y.max():.3f}")


def pandas_examples():
    """Exemplos com Pandas"""
    print("\n🐼 Exemplos Pandas")
    print("=" * 30)
    
    # Criar DataFrame
    data = {
        'nome': ['Ana', 'Bruno', 'Carlos', 'Diana', 'Eduardo'],
        'idade': [25, 30, 35, 28, 32],
        'salario': [5000, 6000, 7500, 5500, 6800],
        'departamento': ['TI', 'RH', 'TI', 'Marketing', 'TI']
    }
    df = pd.DataFrame(data)
    
    print("DataFrame:")
    print(df)
    
    print(f"\nEstatísticas descritivas:")
    print(df.describe())
    
    print(f"\nAgrupamento por departamento:")
    print(df.groupby('departamento').agg({
        'idade': 'mean',
        'salario': ['mean', 'sum']
    }))
    
    # Filtros
    ti_employees = df[df['departamento'] == 'TI']
    print(f"\nFuncionários de TI:")
    print(ti_employees)


def matplotlib_examples():
    """Exemplos com Matplotlib"""
    print("\n📊 Exemplos Matplotlib")
    print("=" * 30)
    
    # Dados para os gráficos
    x = np.linspace(0, 10, 100)
    y1 = np.sin(x)
    y2 = np.cos(x)
    
    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Exemplos Matplotlib', fontsize=16)
    
    # Gráfico de linha
    axes[0, 0].plot(x, y1, 'b-', label='sin(x)')
    axes[0, 0].plot(x, y2, 'r--', label='cos(x)')
    axes[0, 0].set_title('Funções Trigonométricas')
    axes[0, 0].legend()
    axes[0, 0].grid(True)
    
    # Histograma
    data = np.random.normal(0, 1, 1000)
    axes[0, 1].hist(data, bins=30, alpha=0.7, color='green')
    axes[0, 1].set_title('Distribuição Normal')
    axes[0, 1].set_xlabel('Valor')
    axes[0, 1].set_ylabel('Frequência')
    
    # Scatter plot
    x_scatter = np.random.randn(100)
    y_scatter = 2 * x_scatter + np.random.randn(100) * 0.5
    axes[1, 0].scatter(x_scatter, y_scatter, alpha=0.6)
    axes[1, 0].set_title('Scatter Plot')
    axes[1, 0].set_xlabel('X')
    axes[1, 0].set_ylabel('Y')
    
    # Gráfico de barras
    categories = ['A', 'B', 'C', 'D']
    values = [23, 45, 56, 78]
    axes[1, 1].bar(categories, values, color=['red', 'blue', 'green', 'orange'])
    axes[1, 1].set_title('Gráfico de Barras')
    axes[1, 1].set_ylabel('Valores')
    
    plt.tight_layout()
    plt.savefig('matplotlib_examples.png', dpi=150, bbox_inches='tight')
    print("Gráficos salvos em 'matplotlib_examples.png'")
    plt.close()


def seaborn_examples():
    """Exemplos com Seaborn"""
    print("\n🎨 Exemplos Seaborn")
    print("=" * 30)
    
    # Criar dataset de exemplo
    np.random.seed(42)
    n = 200
    df = pd.DataFrame({
        'x': np.random.randn(n),
        'y': np.random.randn(n),
        'categoria': np.random.choice(['A', 'B', 'C'], n),
        'valor': np.random.exponential(2, n)
    })
    
    # Configurar estilo
    sns.set_style("whitegrid")
    
    # Criar figura com subplots
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    fig.suptitle('Exemplos Seaborn', fontsize=16)
    
    # Scatter plot com categorias
    sns.scatterplot(data=df, x='x', y='y', hue='categoria', ax=axes[0, 0])
    axes[0, 0].set_title('Scatter Plot por Categoria')
    
    # Box plot
    sns.boxplot(data=df, x='categoria', y='valor', ax=axes[0, 1])
    axes[0, 1].set_title('Box Plot por Categoria')
    
    # Histograma com densidade
    sns.histplot(data=df, x='valor', kde=True, ax=axes[1, 0])
    axes[1, 0].set_title('Histograma com Densidade')
    
    # Heatmap de correlação
    corr_data = df[['x', 'y', 'valor']].corr()
    sns.heatmap(corr_data, annot=True, cmap='coolwarm', center=0, ax=axes[1, 1])
    axes[1, 1].set_title('Matriz de Correlação')
    
    plt.tight_layout()
    plt.savefig('seaborn_examples.png', dpi=150, bbox_inches='tight')
    print("Gráficos salvos em 'seaborn_examples.png'")
    plt.close()


def scipy_examples():
    """Exemplos com SciPy"""
    print("\n🧮 Exemplos SciPy")
    print("=" * 30)
    
    # Estatísticas
    data = np.random.normal(100, 15, 1000)
    
    # Teste de normalidade
    statistic, p_value = stats.normaltest(data)
    print(f"Teste de normalidade:")
    print(f"  Estatística: {statistic:.4f}")
    print(f"  P-valor: {p_value:.4f}")
    print(f"  É normal? {'Sim' if p_value > 0.05 else 'Não'}")
    
    # Teste t de uma amostra
    t_stat, t_p = stats.ttest_1samp(data, 100)
    print(f"\nTeste t (média = 100):")
    print(f"  Estatística t: {t_stat:.4f}")
    print(f"  P-valor: {t_p:.4f}")
    
    # Correlação
    x = np.random.randn(100)
    y = 2 * x + np.random.randn(100) * 0.5
    corr, corr_p = stats.pearsonr(x, y)
    print(f"\nCorrelação de Pearson:")
    print(f"  Correlação: {corr:.4f}")
    print(f"  P-valor: {corr_p:.4f}")
    
    # Otimização - encontrar mínimo de uma função
    def quadratic(x):
        return x**2 + 2*x + 1
    
    result = optimize.minimize_scalar(quadratic)
    print(f"\nOtimização (mínimo de x² + 2x + 1):")
    print(f"  Mínimo em x = {result.x:.4f}")
    print(f"  Valor mínimo = {result.fun:.4f}")


def main():
    """Executa todos os exemplos"""
    print("🚀 Exemplos Práticos de Análise de Dados")
    print("=" * 50)
    
    numpy_examples()
    pandas_examples()
    matplotlib_examples()
    seaborn_examples()
    scipy_examples()
    
    print("\n✅ Todos os exemplos executados com sucesso!")
    print("\nArquivos gerados:")
    print("- matplotlib_examples.png")
    print("- seaborn_examples.png")


if __name__ == "__main__":
    main()
