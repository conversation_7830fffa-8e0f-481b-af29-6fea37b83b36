# Comandos Úteis - Ambiente de Análise de Dados

## 🚀 Comandos UV (Gerenciador de Pacotes)

### Básicos
```bash
# Executar script Python
uv run python main.py

# Executar Python interativo
uv run python

# Instalar nova dependência
uv add nome-da-biblioteca

# Remover dependência
uv remove nome-da-biblioteca

# Sincronizar dependências
uv sync

# Atualizar dependências
uv lock --upgrade

# Mostrar árvore de dependências
uv tree
```

### Exemplos de Instalação
```bash
# Bibliotecas de machine learning
uv add scikit-learn xgboost

# Jupyter notebook
uv add jupyter

# Bibliotecas de visualização avançada
uv add plotly bokeh

# Bibliotecas de processamento de texto
uv add nltk spacy

# Bibliotecas de deep learning
uv add torch tensorflow

# Bibliotecas de web scraping
uv add requests beautifulsoup4 selenium
```

## 📊 Scripts Disponíveis

### Testar Ambiente
```bash
# Script principal com testes básicos
uv run python main.py

# Exemplos práticos de todas as bibliotecas
uv run python examples.py

# Configurar e testar Kaggle (após configurar credenciais)
uv run python kaggle_setup.py
```

## 🏆 Configuração do Kaggle

### Passo a passo
1. Acesse [https://www.kaggle.com/account](https://www.kaggle.com/account)
2. Clique em "Create New API Token"
3. Baixe o arquivo `kaggle.json`
4. Coloque em uma das localizações:
   - `~/.kaggle/kaggle.json`
   - `~/.config/kaggle/kaggle.json`
5. Configure permissões: `chmod 600 ~/.kaggle/kaggle.json`

### Comandos Kaggle via Python
```python
from kaggle.api.kaggle_api_extended import KaggleApi

api = KaggleApi()
api.authenticate()

# Listar competições
competitions = api.competitions_list()

# Baixar dados de competição
api.competition_download_files('titanic', path='./data', unzip=True)

# Listar datasets
datasets = api.dataset_list(search='covid')

# Baixar dataset
api.dataset_download_files('username/dataset-name', path='./data', unzip=True)
```

## 📈 Exemplos de Código Rápido

### NumPy
```python
import numpy as np

# Array básico
arr = np.array([1, 2, 3, 4, 5])
print(f"Média: {np.mean(arr)}")

# Matriz aleatória
matrix = np.random.randn(3, 3)
print(f"Determinante: {np.linalg.det(matrix)}")
```

### Pandas
```python
import pandas as pd

# Ler CSV
df = pd.read_csv('arquivo.csv')

# Estatísticas básicas
print(df.describe())

# Filtrar dados
filtered = df[df['coluna'] > 10]

# Agrupar dados
grouped = df.groupby('categoria').mean()
```

### Matplotlib
```python
import matplotlib.pyplot as plt

# Gráfico simples
plt.plot([1, 2, 3, 4], [1, 4, 9, 16])
plt.title('Meu Gráfico')
plt.show()

# Salvar gráfico
plt.savefig('grafico.png', dpi=300, bbox_inches='tight')
```

### Seaborn
```python
import seaborn as sns

# Configurar estilo
sns.set_style("whitegrid")

# Scatter plot
sns.scatterplot(data=df, x='x', y='y', hue='categoria')

# Box plot
sns.boxplot(data=df, x='categoria', y='valor')
```

### SciPy
```python
import scipy.stats as stats

# Teste de normalidade
statistic, p_value = stats.normaltest(data)

# Correlação
corr, p_val = stats.pearsonr(x, y)

# Teste t
t_stat, t_p = stats.ttest_1samp(data, expected_mean)
```

## 🔧 Solução de Problemas

### Erro de Importação
```bash
# Verificar se a biblioteca está instalada
uv run python -c "import nome_biblioteca"

# Reinstalar biblioteca
uv remove nome_biblioteca
uv add nome_biblioteca
```

### Problemas com Kaggle
```bash
# Verificar credenciais
ls -la ~/.kaggle/kaggle.json

# Verificar permissões
chmod 600 ~/.kaggle/kaggle.json

# Testar autenticação
uv run python -c "from kaggle.api.kaggle_api_extended import KaggleApi; api = KaggleApi(); api.authenticate(); print('OK')"
```

### Limpar Cache
```bash
# Limpar cache do UV
uv cache clean

# Recriar ambiente virtual
rm -rf .venv
uv sync
```

## 📚 Recursos Úteis

### Documentação
- [NumPy](https://numpy.org/doc/)
- [Pandas](https://pandas.pydata.org/docs/)
- [Matplotlib](https://matplotlib.org/stable/contents.html)
- [Seaborn](https://seaborn.pydata.org/)
- [SciPy](https://docs.scipy.org/)
- [Kaggle API](https://github.com/Kaggle/kaggle-api)

### Datasets Populares no Kaggle
- `titanic` - Dados do Titanic
- `house-prices-advanced-regression-techniques` - Preços de casas
- `digit-recognizer` - Reconhecimento de dígitos
- `nlp-getting-started` - Processamento de linguagem natural
- `covid19-global-forecasting-week-1` - Dados COVID-19

### Jupyter Notebook
```bash
# Instalar Jupyter
uv add jupyter

# Executar Jupyter
uv run jupyter notebook

# Executar JupyterLab
uv add jupyterlab
uv run jupyter lab
```

## 🎯 Próximos Passos

1. **Configure o Kaggle** para acessar datasets
2. **Instale Jupyter** para análise interativa
3. **Explore datasets** do Kaggle
4. **Pratique** com os exemplos fornecidos
5. **Adicione bibliotecas** conforme necessário
