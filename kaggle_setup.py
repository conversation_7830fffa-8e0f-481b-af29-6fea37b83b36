"""
Script para configurar e testar a API do Kaggle
Execute este script após configurar suas credenciais do Kaggle
"""

import os
import json
from pathlib import Path

try:
    from kaggle.api.kaggle_api_extended import KaggleApi
    import pandas as pd
    KAGGLE_AVAILABLE = True
except ImportError:
    KAGGLE_AVAILABLE = False


def check_kaggle_credentials():
    """Verifica se as credenciais do Kaggle estão configuradas"""
    print("🔍 Verificando credenciais do Kaggle...")
    
    # Possíveis localizações do arquivo kaggle.json
    possible_paths = [
        Path.home() / '.kaggle' / 'kaggle.json',
        Path.home() / '.config' / 'kaggle' / 'kaggle.json'
    ]
    
    for path in possible_paths:
        if path.exists():
            print(f"✅ Credenciais encontradas em: {path}")
            
            # Verificar permissões
            stat = path.stat()
            permissions = oct(stat.st_mode)[-3:]
            
            if permissions == '600':
                print("✅ Permissões corretas (600)")
            else:
                print(f"⚠️  Permissões incorretas ({permissions}). Execute: chmod 600 {path}")
            
            # Verificar conteúdo
            try:
                with open(path, 'r') as f:
                    creds = json.load(f)
                    if 'username' in creds and 'key' in creds:
                        print("✅ Arquivo de credenciais válido")
                        return True
                    else:
                        print("❌ Arquivo de credenciais inválido (faltam username ou key)")
                        return False
            except json.JSONDecodeError:
                print("❌ Arquivo de credenciais com formato JSON inválido")
                return False
    
    print("❌ Credenciais não encontradas")
    return False


def test_kaggle_api():
    """Testa a API do Kaggle"""
    if not KAGGLE_AVAILABLE:
        print("❌ Biblioteca kaggle não disponível")
        return False
    
    try:
        print("\n🧪 Testando API do Kaggle...")
        api = KaggleApi()
        api.authenticate()
        print("✅ Autenticação bem-sucedida!")
        
        # Testar listagem de datasets
        print("\n📊 Listando datasets populares:")
        datasets = api.dataset_list(sort_by='hottest', page=1)[:5]
        for i, dataset in enumerate(datasets, 1):
            print(f"{i}. {dataset.ref} - {dataset.title}")
            # Alguns datasets podem não ter informação de tamanho
            size_info = getattr(dataset, 'totalBytes', None)
            if size_info:
                size_mb = size_info // 1024 // 1024
                print(f"   Tamanho: {size_mb} MB")
            else:
                print(f"   Tamanho: N/A")
        
        # Testar listagem de competições
        print("\n🏆 Competições ativas:")
        competitions = api.competitions_list(page=1, search='')[:3]
        for comp in competitions:
            print(f"- {comp.ref}: {comp.title}")
            print(f"  Deadline: {comp.deadline}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao testar API: {e}")
        return False


def download_sample_data():
    """Baixa dados de exemplo do Kaggle"""
    if not KAGGLE_AVAILABLE:
        return
    
    try:
        print("\n📥 Baixando dataset de exemplo (Titanic)...")
        api = KaggleApi()
        api.authenticate()
        
        # Criar diretório data se não existir
        data_dir = Path('./data')
        data_dir.mkdir(exist_ok=True)
        
        # Baixar dataset Titanic
        api.competition_download_files('titanic', path=str(data_dir), quiet=False)
        
        # Extrair arquivos se necessário
        import zipfile
        zip_path = data_dir / 'titanic.zip'
        if zip_path.exists():
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(data_dir)
            zip_path.unlink()  # Remove o arquivo zip
            print("✅ Arquivos extraídos com sucesso!")
        
        # Verificar arquivos baixados
        csv_files = list(data_dir.glob('*.csv'))
        if csv_files:
            print(f"\n📁 Arquivos baixados:")
            for file in csv_files:
                print(f"- {file.name}")
                
            # Carregar e mostrar informações do train.csv
            train_file = data_dir / 'train.csv'
            if train_file.exists():
                df = pd.read_csv(train_file)
                print(f"\n📊 Dataset Titanic (train.csv):")
                print(f"- Linhas: {len(df)}")
                print(f"- Colunas: {len(df.columns)}")
                print(f"- Colunas: {list(df.columns)}")
                print(f"\nPrimeiras 3 linhas:")
                print(df.head(3))
        
    except Exception as e:
        print(f"❌ Erro ao baixar dados: {e}")


def show_kaggle_commands():
    """Mostra comandos úteis da API do Kaggle"""
    print("\n💡 Comandos úteis da API do Kaggle:")
    print("=" * 50)
    
    commands = [
        ("Listar competições", "api.competitions_list()"),
        ("Baixar dados de competição", "api.competition_download_files('competition-name', path='./data')"),
        ("Listar datasets", "api.dataset_list(search='keyword')"),
        ("Baixar dataset", "api.dataset_download_files('username/dataset-name', path='./data')"),
        ("Submeter para competição", "api.competition_submit('submission.csv', 'message', 'competition-name')"),
        ("Ver leaderboard", "api.competition_leaderboard_view('competition-name')"),
        ("Listar seus datasets", "api.dataset_list(user='your-username')"),
        ("Criar novo dataset", "api.dataset_create_new(folder, public=True)")
    ]
    
    for desc, cmd in commands:
        print(f"• {desc}:")
        print(f"  {cmd}")
        print()


def main():
    """Função principal"""
    print("🏆 Configuração e Teste da API do Kaggle")
    print("=" * 50)
    
    if not KAGGLE_AVAILABLE:
        print("❌ Biblioteca kaggle não está instalada!")
        print("Execute: uv add kaggle")
        return
    
    # Verificar credenciais
    creds_ok = check_kaggle_credentials()
    
    if not creds_ok:
        print("\n📋 Para configurar as credenciais do Kaggle:")
        print("1. Vá para https://www.kaggle.com/account")
        print("2. Clique em 'Create New API Token'")
        print("3. Baixe o arquivo kaggle.json")
        print("4. Coloque em ~/.kaggle/kaggle.json ou ~/.config/kaggle/kaggle.json")
        print("5. Execute: chmod 600 ~/.kaggle/kaggle.json")
        return
    
    # Testar API
    api_ok = test_kaggle_api()
    
    if api_ok:
        # Baixar dados de exemplo
        download_sample_data()
        
        # Mostrar comandos úteis
        show_kaggle_commands()
        
        print("\n✅ Kaggle configurado e testado com sucesso!")
        print("\nAgora você pode:")
        print("- Baixar datasets para análise")
        print("- Participar de competições")
        print("- Submeter soluções")
        print("- Explorar dados públicos")
    else:
        print("\n❌ Falha na configuração do Kaggle")


if __name__ == "__main__":
    main()
