# Ariel - Ambiente de Análise de Dados

Ambiente Python configurado com UV para análise de dados, incluindo todas as bibliotecas essenciais e acesso ao Kaggle.

## 🛠️ Bibliotecas Instaladas

- **NumPy** - Computação numérica
- **Pandas** - Manipulação e análise de dados
- **Matplotlib** - Visualização de dados
- **Seaborn** - Visualização estatística
- **SciPy** - Computação científica
- **Kaggle** - API para baixar datasets do Kaggle

## 🚀 Como Usar

### Executar o script de teste
```bash
uv run python main.py
```

### Instalar novas dependências
```bash
uv add nome-da-biblioteca
```

### Executar Python interativo
```bash
uv run python
```

### Executar Jupyter Notebook (opcional)
```bash
uv add jupyter
uv run jupyter notebook
```

## 📊 Configuração do Kaggle

Para usar a API do Kaggle e baixar datasets:

1. Vá para [https://www.kaggle.com/account](https://www.kaggle.com/account)
2. Clique em "Create New API Token"
3. Baixe o arquivo `kaggle.json`
4. Coloque o arquivo em `~/.kaggle/kaggle.json`
5. Execute: `chmod 600 ~/.kaggle/kaggle.json`

## 📁 Estrutura do Projeto

```
ariel/
├── main.py          # Script principal com exemplos
├── pyproject.toml    # Configuração do projeto
├── uv.lock          # Lock file das dependências
├── README.md        # Este arquivo
└── data/            # Pasta para datasets (criada automaticamente)
```

## 💡 Exemplos de Uso

### Análise básica com Pandas
```python
import pandas as pd
import numpy as np

# Criar DataFrame
df = pd.DataFrame({
    'valores': np.random.randn(100),
    'categoria': np.random.choice(['A', 'B', 'C'], 100)
})

# Estatísticas básicas
print(df.describe())
print(df.groupby('categoria').mean())
```

### Visualização com Matplotlib e Seaborn
```python
import matplotlib.pyplot as plt
import seaborn as sns

# Configurar estilo
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Criar gráfico
plt.figure(figsize=(10, 6))
sns.boxplot(data=df, x='categoria', y='valores')
plt.title('Distribuição por Categoria')
plt.show()
```

### Baixar dados do Kaggle
```python
from kaggle.api.kaggle_api_extended import KaggleApi

api = KaggleApi()
api.authenticate()

# Baixar dataset
api.dataset_download_files('titanic', path='./data', unzip=True)

# Carregar dados
df = pd.read_csv('./data/train.csv')
print(df.head())
```

## 🔧 Comandos Úteis

- `uv sync` - Sincronizar dependências
- `uv lock` - Atualizar lock file
- `uv tree` - Mostrar árvore de dependências
- `uv run --help` - Ajuda do comando run