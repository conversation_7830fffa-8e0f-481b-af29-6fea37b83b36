"""
Script de exemplo para testar o ambiente de análise de dados
Inclui todas as bibliotecas instaladas: numpy, pandas, matplotlib, seaborn, scipy e kaggle
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.stats as stats
import os

# Importação opcional do Kaggle
try:
    from kaggle.api.kaggle_api_extended import KaggleApi
    KAGGLE_AVAILABLE = True
except (ImportError, OSError):
    KAGGLE_AVAILABLE = False
    print("⚠️  Kaggle API não disponível (credenciais não configuradas)")


def test_libraries():
    """Testa todas as bibliotecas instaladas"""
    print("🔬 Testando bibliotecas de análise de dados...")

    # Teste NumPy
    print("\n📊 NumPy:")
    arr = np.array([1, 2, 3, 4, 5])
    print(f"Array: {arr}")
    print(f"Média: {np.mean(arr)}")

    # Teste Pandas
    print("\n🐼 Pandas:")
    df = pd.DataFrame({
        'A': [1, 2, 3, 4, 5],
        'B': [10, 20, 30, 40, 50],
        'C': ['a', 'b', 'c', 'd', 'e']
    })
    print("DataFrame criado:")
    print(df)

    # Teste SciPy
    print("\n🧮 SciPy:")
    data = np.random.normal(0, 1, 100)
    statistic, p_value = stats.normaltest(data)
    print(f"Teste de normalidade - p-value: {p_value:.4f}")

    # Teste Matplotlib e Seaborn
    print("\n📈 Matplotlib e Seaborn:")
    plt.figure(figsize=(10, 4))

    # Subplot 1: Matplotlib
    plt.subplot(1, 2, 1)
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    plt.plot(x, y)
    plt.title('Matplotlib: Função Seno')
    plt.xlabel('x')
    plt.ylabel('sin(x)')

    # Subplot 2: Seaborn
    plt.subplot(1, 2, 2)
    sns.scatterplot(data=df, x='A', y='B')
    plt.title('Seaborn: Scatter Plot')

    plt.tight_layout()
    plt.savefig('test_plot.png', dpi=150, bbox_inches='tight')
    print("Gráfico salvo como 'test_plot.png'")
    plt.close()


def setup_kaggle():
    """Configura e testa a API do Kaggle"""
    print("\n🏆 Configurando Kaggle API...")

    if not KAGGLE_AVAILABLE:
        print("⚠️  Kaggle API não disponível!")
        print("Para usar a API do Kaggle, você precisa:")
        print("1. Ir para https://www.kaggle.com/account")
        print("2. Clicar em 'Create New API Token'")
        print("3. Baixar o arquivo kaggle.json")
        print("4. Colocar o arquivo em ~/.kaggle/kaggle.json ou ~/.config/kaggle/kaggle.json")
        print("5. Executar: chmod 600 ~/.kaggle/kaggle.json")
        return False

    # Verifica se as credenciais do Kaggle existem
    kaggle_dirs = [
        os.path.expanduser('~/.kaggle'),
        os.path.expanduser('~/.config/kaggle')
    ]

    kaggle_json_found = False
    for kaggle_dir in kaggle_dirs:
        kaggle_json = os.path.join(kaggle_dir, 'kaggle.json')
        if os.path.exists(kaggle_json):
            kaggle_json_found = True
            break

    if not kaggle_json_found:
        print("⚠️  Credenciais do Kaggle não encontradas!")
        print("Para usar a API do Kaggle, você precisa:")
        print("1. Ir para https://www.kaggle.com/account")
        print("2. Clicar em 'Create New API Token'")
        print("3. Baixar o arquivo kaggle.json")
        print("4. Colocar o arquivo em ~/.kaggle/kaggle.json ou ~/.config/kaggle/kaggle.json")
        print("5. Executar: chmod 600 ~/.kaggle/kaggle.json")
        return False

    try:
        api = KaggleApi()
        api.authenticate()
        print("✅ Kaggle API configurada com sucesso!")

        # Lista alguns datasets populares
        print("\n📊 Alguns datasets populares:")
        datasets = api.dataset_list(search='titanic', page_size=3)
        for dataset in datasets:
            print(f"- {dataset.ref}: {dataset.title}")

        return True
    except Exception as e:
        print(f"❌ Erro ao configurar Kaggle API: {e}")
        return False


def download_sample_dataset():
    """Baixa um dataset de exemplo do Kaggle"""
    print("\n📥 Baixando dataset de exemplo...")

    if not KAGGLE_AVAILABLE:
        print("⚠️  Kaggle API não disponível - pulando download")
        return

    try:
        api = KaggleApi()
        api.authenticate()

        # Baixa o famoso dataset Titanic
        api.dataset_download_files('titanic', path='./data', unzip=True)
        print("✅ Dataset Titanic baixado para ./data/")

        # Carrega e mostra informações básicas
        if os.path.exists('./data/train.csv'):
            df = pd.read_csv('./data/train.csv')
            print(f"\n📊 Dataset carregado: {df.shape[0]} linhas, {df.shape[1]} colunas")
            print("\nPrimeiras 5 linhas:")
            print(df.head())
            print("\nInformações do dataset:")
            print(df.info())

    except Exception as e:
        print(f"❌ Erro ao baixar dataset: {e}")


def main():
    """Função principal"""
    print("🚀 Ambiente de Análise de Dados com UV")
    print("=" * 50)

    # Testa todas as bibliotecas
    test_libraries()

    # Configura Kaggle
    kaggle_configured = setup_kaggle()

    # Se Kaggle estiver configurado, baixa um dataset de exemplo
    if kaggle_configured:
        download_sample_dataset()

    print("\n✅ Ambiente configurado e testado com sucesso!")
    print("\nPróximos passos:")
    print("- Use 'uv run python main.py' para executar este script")
    print("- Use 'uv add <package>' para adicionar novas dependências")
    print("- Use 'uv run python -c \"import <library>\"' para testar bibliotecas")


if __name__ == "__main__":
    main()
